import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { ButtonComponent } from './index';

const meta = {
	title: 'ESW/Button',
	component: ButtonComponent,
	parameters: {
		layout: 'centered',
		docs: {
			description: {
				component: 'Common button component for reusing everywhere.',
			},
		},
	},
	tags: ['autodocs'],
	argTypes: {
		variant: { control: 'radio' },
	},
	args: { onClick: fn() },
} satisfies Meta<typeof ButtonComponent>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
	args: {
		children: 'Default',
	},
};
