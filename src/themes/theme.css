@import url('https://fonts.googleapis.com/css2?family=Public+Sans:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@100;200;300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import 'tailwindcss';
@theme {
	/* Colors */
	--color-brand-50: rgb(238, 242, 255);
	--color-brand-100: rgb(224, 231, 255);
	--color-brand-200: rgb(199, 210, 254);
	--color-brand-300: rgb(165, 180, 252);
	--color-brand-400: rgb(129, 140, 248);
	--color-brand-500: rgb(99, 102, 241);
	--color-brand-600: rgb(79, 70, 229);
	--color-brand-700: rgb(67, 56, 202);
	--color-brand-800: rgb(55, 48, 163);
	--color-brand-900: rgb(49, 46, 129);
	--color-brand-950: rgb(30, 27, 75);
	--color-neutral-0: rgb(255, 255, 255);
	--color-neutral-50: rgb(248, 250, 252);
	--color-neutral-100: rgb(241, 245, 249);
	--color-neutral-200: rgb(226, 232, 240);
	--color-neutral-300: rgb(203, 213, 225);
	--color-neutral-400: rgb(148, 163, 184);
	--color-neutral-500: rgb(100, 116, 139);
	--color-neutral-600: rgb(71, 85, 105);
	--color-neutral-700: rgb(51, 65, 85);
	--color-neutral-800: rgb(30, 41, 59);
	--color-neutral-900: rgb(15, 23, 42);
	--color-neutral-950: rgb(2, 6, 23);
	--color-error-50: rgb(254, 242, 242);
	--color-error-100: rgb(254, 226, 226);
	--color-error-200: rgb(254, 202, 202);
	--color-error-300: rgb(252, 165, 165);
	--color-error-400: rgb(248, 113, 113);
	--color-error-500: rgb(239, 68, 68);
	--color-error-600: rgb(220, 38, 38);
	--color-error-700: rgb(185, 28, 28);
	--color-error-800: rgb(153, 27, 27);
	--color-error-900: rgb(127, 29, 29);
	--color-error-950: rgb(69, 10, 10);
	--color-warning-50: rgb(255, 251, 235);
	--color-warning-100: rgb(254, 243, 199);
	--color-warning-200: rgb(253, 230, 138);
	--color-warning-300: rgb(252, 211, 77);
	--color-warning-400: rgb(251, 191, 36);
	--color-warning-500: rgb(245, 158, 11);
	--color-warning-600: rgb(217, 119, 6);
	--color-warning-700: rgb(180, 83, 9);
	--color-warning-800: rgb(146, 64, 14);
	--color-warning-900: rgb(120, 53, 15);
	--color-warning-950: rgb(69, 26, 3);
	--color-success-50: rgb(236, 253, 245);
	--color-success-100: rgb(209, 250, 229);
	--color-success-200: rgb(167, 243, 208);
	--color-success-300: rgb(110, 231, 183);
	--color-success-400: rgb(52, 211, 153);
	--color-success-500: rgb(16, 185, 129);
	--color-success-600: rgb(5, 150, 105);
	--color-success-700: rgb(4, 120, 87);
	--color-success-800: rgb(6, 95, 70);
	--color-success-900: rgb(6, 78, 59);
	--color-success-950: rgb(2, 44, 34);
	--color-info-50: rgb(240, 249, 255);
	--color-info-100: rgb(224, 242, 254);
	--color-info-200: rgb(186, 230, 253);
	--color-info-300: rgb(125, 211, 252);
	--color-info-400: rgb(56, 189, 248);
	--color-info-500: rgb(14, 165, 233);
	--color-info-600: rgb(2, 132, 199);
	--color-info-700: rgb(3, 105, 161);
	--color-info-800: rgb(7, 89, 133);
	--color-info-900: rgb(12, 74, 110);
	--color-info-950: rgb(8, 47, 73);
	--color-brand-primary: rgb(79, 70, 229);
	--color-default-font: rgb(15, 23, 42);
	--color-subtext-color: rgb(100, 116, 139);
	--color-neutral-border: rgb(226, 232, 240);
	--color-white: rgb(255, 255, 255);
	--color-default-background: rgb(255, 255, 255);

	/* Fonts */
	--text-caption: 12px;
	--text-caption--font-weight: 400;
	--text-caption--letter-spacing: 0em;
	--text-caption--line-height: 16px;
	--text-caption-bold: 12px;
	--text-caption-bold--font-weight: 600;
	--text-caption-bold--letter-spacing: 0em;
	--text-caption-bold--line-height: 16px;
	--text-body: 14px;
	--text-body--font-weight: 400;
	--text-body--letter-spacing: 0em;
	--text-body--line-height: 20px;
	--text-body-bold: 14px;
	--text-body-bold--font-weight: 600;
	--text-body-bold--letter-spacing: 0em;
	--text-body-bold--line-height: 20px;
	--text-heading-3: 16px;
	--text-heading-3--font-weight: 700;
	--text-heading-3--letter-spacing: 0em;
	--text-heading-3--line-height: 20px;
	--text-heading-2: 20px;
	--text-heading-2--font-weight: 700;
	--text-heading-2--letter-spacing: 0em;
	--text-heading-2--line-height: 24px;
	--text-heading-1: 30px;
	--text-heading-1--font-weight: 700;
	--text-heading-1--letter-spacing: 0em;
	--text-heading-1--line-height: 36px;
	--text-monospace-body: 14px;
	--text-monospace-body--font-weight: 400;
	--text-monospace-body--letter-spacing: 0em;
	--text-monospace-body--line-height: 20px;
	--text-custom-text: 14px;
	--text-custom-text--font-weight: 400;
	--text-custom-text--letter-spacing: 0em;
	--text-custom-text--line-height: 20px;

	/* Font families */
	--font-caption: 'Public Sans';
	--font-caption-bold: 'Public Sans';
	--font-body: 'Public Sans';
	--font-body-bold: 'Public Sans';
	--font-heading-3: 'Public Sans';
	--font-heading-2: 'Public Sans';
	--font-heading-1: 'Public Sans';
	--font-monospace-body: 'Roboto Mono';
	--font-custom-text: 'Public Sans';

	/* Box shadows */
	--shadow-sm: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
	--shadow-default: 0px 1px 2px 0px rgba(0, 0, 0, 0.05);
	--shadow-md: 0px 4px 16px -2px rgba(0, 0, 0, 0.08), 0px 2px 4px -1px rgba(0, 0, 0, 0.08);
	--shadow-lg: 0px 12px 32px -4px rgba(0, 0, 0, 0.08), 0px 4px 8px -2px rgba(0, 0, 0, 0.08);
	--shadow-overlay: 0px 12px 32px -4px rgba(0, 0, 0, 0.08), 0px 4px 8px -2px rgba(0, 0, 0, 0.08);

	/* Border radiuses */
	--radius-sm: 8px;
	--radius-md: 16px;
	--radius-DEFAULT: 16px;
	--radius-lg: 24px;
	--radius-full: 9999px;

	/* Spacing */
	--spacing-112: 28rem;
	--spacing-144: 36rem;
	--spacing-192: 48rem;
	--spacing-256: 64rem;
	--spacing-320: 80rem;
}

/* Container */
@utility container {
	padding-left: 16px;
	padding-right: 16px;

	@media (width >= theme(--breakpoint-sm)) {
		padding-left: calc((100vw + 16px - 640px) / 2);
		padding-right: calc((100vw + 16px - 640px) / 2);
	}

	@media (width >= theme(--breakpoint-md)) {
		padding-left: calc((100vw + 16px - 768px) / 2);
		padding-right: calc((100vw + 16px - 768px) / 2);
	}

	@media (width >= theme(--breakpoint-lg)) {
		padding-left: calc((100vw + 16px - 1024px) / 2);
		padding-right: calc((100vw + 16px - 1024px) / 2);
	}

	@media (width >= theme(--breakpoint-xl)) {
		padding-left: calc((100vw + 16px - 1280px) / 2);
		padding-right: calc((100vw + 16px - 1280px) / 2);
	}

	@media (width >= theme(--breakpoint-2xl)) {
		padding-left: calc((100vw + 16px - 1536px) / 2);
		padding-right: calc((100vw + 16px - 1536px) / 2);
	}
}

@custom-variant mobile (@media (max-width: 767px));
