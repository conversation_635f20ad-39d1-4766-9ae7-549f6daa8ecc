'use client';
/*
 * Documentation:
 * Pool_Bracket Card — https://app.subframe.com/5ed7fb16a283/library?component=Pool_Bracket+Card_92829e7e-e9ee-4e7f-b251-0d4ca36022b6
 */

import React from 'react';
import * as SubframeUtils from '../utils';
import * as SubframeCore from '@subframe/core';
import { FeatherWorkflow } from '@subframe/core';

interface Pool_BracketCardRootProps extends React.HTMLAttributes<HTMLDivElement> {
	icon?: React.ReactNode;
	bracketName?: React.ReactNode;
	startTime?: React.ReactNode;
	bracketType?: React.ReactNode;
	courtLabel?: React.ReactNode;
	courtList?: React.ReactNode;
	teamLabel?: React.ReactNode;
	teamList?: React.ReactNode;
	expanded?: boolean;
	children?: React.ReactNode;
	className?: string;
}

const Pool_BracketCardRoot = React.forwardRef<HTMLElement, Pool_BracketCardRootProps>(
	function Pool_BracketCardRoot(
		{
			icon = <FeatherWorkflow />,
			bracketName,
			startTime,
			bracketType,
			courtLabel,
			courtList,
			teamLabel,
			teamList,
			expanded = false,
			children,
			className,
			...otherProps
		}: Pool_BracketCardRootProps,
		ref,
	) {
		return (
			<div
				className={SubframeUtils.twClassNames(
					'group/92829e7e flex w-full flex-col items-start rounded-md border border-solid border-neutral-border shadow-md',
					className,
				)}
				ref={ref as any}
				{...otherProps}
			>
				<div className="flex w-full items-center justify-between rounded-t-md border-b border-solid border-neutral-border bg-default-background px-3 pt-3 pb-2">
					<div className="flex items-center gap-2">
						{icon ? (
							<SubframeCore.IconWrapper className="text-heading-3 font-heading-3 text-brand-700">
								{icon}
							</SubframeCore.IconWrapper>
						) : null}
						{bracketName ? (
							<span className="text-heading-2 font-heading-2 text-default-font">{bracketName}</span>
						) : null}
					</div>
					<div className="flex flex-col items-end">
						{startTime ? (
							<span
								className={SubframeUtils.twClassNames('text-body font-body text-brand-600', {
									'text-brand-primary': expanded,
								})}
							>
								{startTime}
							</span>
						) : null}
						{bracketType ? (
							<span
								className={SubframeUtils.twClassNames('text-body font-body text-brand-600', {
									'text-brand-primary': expanded,
								})}
							>
								{bracketType}
							</span>
						) : null}
					</div>
				</div>
				<div className="flex w-full flex-col items-start gap-2 rounded-b-md bg-brand-50 px-2 py-2">
					<div className="flex w-full items-start gap-2 rounded-sm border border-solid border-neutral-border bg-default-background px-2 py-2">
						{courtLabel ? (
							<span className="text-body-bold font-body-bold text-brand-600">{courtLabel}</span>
						) : null}
						{courtList ? (
							<span className="text-body font-body text-neutral-700">{courtList}</span>
						) : null}
					</div>
					{children ? (
						<div className="flex w-full items-start gap-2 rounded-sm border border-solid border-neutral-border bg-default-background px-2 py-2">
							{children}
						</div>
					) : null}
				</div>
			</div>
		);
	},
);

export const Pool_BracketCard = Pool_BracketCardRoot;
