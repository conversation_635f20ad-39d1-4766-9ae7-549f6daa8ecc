import type { Preview, Decorator } from '@storybook/react';

const themeMap: Record<string, string> = {
	theme1: new URL('../src/themes/theme.css', import.meta.url).toString(),
	theme2: new URL('../src/themes/theme2.css', import.meta.url).toString(),
};

function applyThemeCss(theme: string) {
	const id = 'storybook-theme-style';
	let link = document.getElementById(id) as HTMLLinkElement | null;

	if (!link) {
		link = document.createElement('link');
		link.id = id;
		link.rel = 'stylesheet';
		document.head.appendChild(link);
	}

	link.href = themeMap[theme] || themeMap['theme1'];
}

const withTheme: Decorator = (StoryFn, context) => {
	const selectedTheme = context.globals.theme || 'theme1';
	applyThemeCss(selectedTheme);
	return StoryFn();
};

const preview: Preview = {
	parameters: {
		controls: {
			matchers: {
				color: /(background|color)$/i,
				date: /Date$/i,
			},
		},
	},
	globalTypes: {
		theme: {
			name: 'Theme',
			description: 'Switch themes',
			defaultValue: 'theme1',
			toolbar: {
				icon: 'paintbrush',
				items: [
					{ value: 'theme1', title: 'Theme 1' },
					{ value: 'theme2', title: 'Theme 2' },
				],
				showName: true,
			},
		},
	},
	decorators: [withTheme],
};

export default preview;
