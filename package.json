{"name": "@sportwrench/ui", "version": "0.1.2", "type": "module", "scripts": {"lint": "eslint .", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "build:js": "tsup src/index.ts --dts --format esm,cjs --out-dir dist --tsconfig tsconfig.app.json", "build:theme1": "postcss src/themes/theme.css -o dist/themes/theme1.css", "build:theme2": "postcss src/themes/theme2.css -o dist/themes/theme2.css", "build": "npm run build:js && npm run build:theme1 && npm run build:theme2", "prepare": "husky"}, "dependencies": {"@subframe/core": "^1.145.0", "@tailwindcss/vite": "^4.1.11"}, "devDependencies": {"@chromatic-com/storybook": "^3.2.6", "@eslint/js": "^9.22.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/experimental-addon-test": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/react-vite": "^8.6.12", "@storybook/test": "^8.6.12", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.12", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "@vitest/browser": "^3.1.2", "@vitest/coverage-v8": "^3.1.2", "autoprefixer": "^10.4.21", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-storybook": "^0.12.0", "globals": "^16.0.0", "husky": "^9.1.7", "playwright": "^1.52.0", "postcss": "^8.5.6", "postcss-cli": "^11.0.1", "prettier": "^3.5.3", "storybook": "^8.6.12", "tailwindcss": "^4.1.11", "tsup": "^8.4.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1", "vite-plugin-css-injected-by-js": "^3.5.2", "vitest": "^3.1.2"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "publishConfig": {"access": "public"}, "files": ["dist"], "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./themes/theme1.css": "./dist/themes/theme1.css", "./themes/theme2.css": "./dist/themes/theme2.css"}}